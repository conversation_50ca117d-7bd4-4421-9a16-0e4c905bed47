package util

import (
	"fmt"
	"strconv"
)

// 安全地将interface{}转换为int64
// 支持多种数值类型的转换，包括字符串数值，避免类型断言失败
// 参数:
//   - value: 需要转换的interface{}值
// 返回:
//   - int64: 转换后的int64值，转换失败时返回0
//   - bool: 转换是否成功
func SafeToInt64(value interface{}) (int64, bool) {
	if value == nil {
		return 0, false
	}

	switch v := value.(type) {
	case int64:
		return v, true
	case int:
		return int64(v), true
	case int32:
		return int64(v), true
	case int16:
		return int64(v), true
	case int8:
		return int64(v), true
	case uint64:
		return int64(v), true
	case uint:
		return int64(v), true
	case uint32:
		return int64(v), true
	case uint16:
		return int64(v), true
	case uint8:
		return int64(v), true
	case float64:
		return int64(v), true
	case float32:
		return int64(v), true
	case string:
		// 尝试将字符串转换为数值
		if result, err := strconv.ParseInt(v, 10, 64); err == nil {
			return result, true
		}
		// 尝试先转换为float64再转为int64
		if result, err := strconv.ParseFloat(v, 64); err == nil {
			return int64(result), true
		}
		return 0, false
	default:
		return 0, false
	}
}

// 安全地将interface{}转换为int
// 内部调用SafeToInt64然后转换为int类型
// 参数:
//   - value: 需要转换的interface{}值
// 返回:
//   - int: 转换后的int值，转换失败时返回0
//   - bool: 转换是否成功
func SafeToInt(value interface{}) (int, bool) {
	if result, ok := SafeToInt64(value); ok {
		return int(result), true
	}
	return 0, false
}

// 安全地将interface{}转换为string
// 支持多种类型到字符串的转换，包括所有数值类型
// 参数:
//   - value: 需要转换的interface{}值
// 返回:
//   - string: 转换后的字符串值，转换失败时返回空字符串
//   - bool: 转换是否成功
func SafeToString(value interface{}) (string, bool) {
	if value == nil {
		return "", false
	}

	switch v := value.(type) {
	case string:
		return v, true
	case []byte:
		return string(v), true
	// 整数类型转换
	case int:
		return strconv.Itoa(v), true
	case int8:
		return strconv.FormatInt(int64(v), 10), true
	case int16:
		return strconv.FormatInt(int64(v), 10), true
	case int32:
		return strconv.FormatInt(int64(v), 10), true
	case int64:
		return strconv.FormatInt(v, 10), true
	// 无符号整数类型转换
	case uint:
		return strconv.FormatUint(uint64(v), 10), true
	case uint8:
		return strconv.FormatUint(uint64(v), 10), true
	case uint16:
		return strconv.FormatUint(uint64(v), 10), true
	case uint32:
		return strconv.FormatUint(uint64(v), 10), true
	case uint64:
		return strconv.FormatUint(v, 10), true
	// 浮点数类型转换
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32), true
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64), true
	// 布尔类型转换
	case bool:
		return strconv.FormatBool(v), true
	default:
		// 对于其他类型，使用fmt.Sprintf转换
		return fmt.Sprintf("%v", v), true
	}
}
