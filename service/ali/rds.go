package ali

import (
	"fmt"
	"net/smtp"
	"strings"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	openapiutil "github.com/alibabacloud-go/openapi-util/service"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

// RDS慢查询日志配置
type RDSSlowLogConfig struct {
	// 邮件接收人
	Recipients []string
	// 查询时间范围（小时）
	QueryHoursAgo int
	// 慢查询阈值（毫秒）
	SlowThreshold int
	// 区域ID
	RegionId string
	// 分页大小
	PageSize int
	// 需要忽略的实例
	IgnoreDBInstanceID []string
	// SMTP配置
	SMTPServer   string
	SMTPPort     int
	SMTPUser     string
	SMTPPassword string
	SMTPFrom     string
}

// RDS实例信息
type DBInstance struct {
	DBInstanceId          string // 实例ID
	DBInstanceType        string // 实例类型：Primary（主实例）、Readonly（只读实例）、Guard（灾备实例）、Temp（临时实例）
	DBInstanceStatus      string // 实例状态：Creating、Running、Deleting、Rebooting等
	RegionId              string // 地域ID
	Engine                string // 数据库类型
	EngineVersion         string // 数据库版本
	DBInstanceClass       string // 实例规格
	DBInstanceStorage     int    // 实例存储空间，单位：GB
	DBInstanceNetType     string // 实例的网络连接类型：Internet、Intranet
	DBInstanceDesc        string // 实例描述
	ConnectionMode        string // 实例的访问模式：Standard、Safe
	LockMode              string // 实例的锁定状态
	Category              string // 实例系列：Basic（基础版）、HighAvailability（高可用版）、AlwaysOn（集群版）、Finance（金融版）
	PayType               string // 实例的付费类型：Postpaid（按量付费）、Prepaid（包年包月）
	ConnectionString      string // 实例的内网连接地址
	Port                  string // 实例的端口地址
	ExpireTime            string // 实例到期时间，按量付费实例无到期时间
	ResourceGroupId       string // 资源组ID
	DBInstanceStorageType string // 实例存储类型：local_ssd、cloud_ssd、cloud_essd
	MaintainTime          string // 实例可维护时间段
	AvailabilityValue     string // 实例在一个月内的可用性
	MaxIOPS               int    // 最大每秒IO请求次数
	MaxConnections        int    // 最大连接数
	CreationTime          string // 创建时间
}

// 慢查询日志记录
// 根据阿里云RDS MySQL版API文档：https://help.aliyun.com/zh/rds/apsaradb-rds-for-mysql/api-rds-2014-08-15-describeslowlogrecords-mysql
// SQLSlowRecord 返回参数结构
type SlowLogRecord struct {
	ApplicationName       string // 连接的应用名称（仅 SQL Server 实例支持）
	ClientHostName        string // 客户端主机名（仅 SQL Server 实例支持）
	CpuTime               int64  // CPU 处理时长，单位：毫秒（仅 SQL Server 实例支持）
	DBName                string // 数据库名称
	ExecutionStartTime    string // 执行开始时间，格式：yyyy-MM-ddTHH:mm:ssZ（UTC 时间）
	HostAddress           string // 连接数据库的客户端名称及地址
	LastRowsAffectedCount int64  // 最后一条语句的影响行数（仅 SQL Server 实例支持）
	LockTimes             int64  // 锁定时长，单位：秒
	LogicalIORead         int64  // 逻辑读次数（仅 SQL Server 实例支持）
	ParseRowCounts        int64  // 解析行数
	PhysicalIORead        int64  // 物理读次数（仅 SQL Server 实例支持）
	QueryTimeMS           int64  // 执行时长，单位：毫秒（对于 SQL Server，该参数的单位为微秒）
	QueryTimes            int64  // 执行时长，单位：秒（对于 SQL Server，该参数的单位为毫秒）
	ReturnRowCounts       int64  // 返回行数
	RowsAffectedCount     int64  // 影响行数（仅 SQL Server 实例支持）
	SQLHash               string // 慢日志明细里的 SQL 语句唯一标识符
	SQLText               string // SQL 命令详情
	UserName              string // 用户名（仅 SQL Server 实例支持）
	WriteIOCount          int64  // I/O 写入次数（仅 SQL Server 实例支持）
}

// @return OpenApi.Params
func CreateRdsApiInfo(action string) (_result *openapi.Params) {
	params := &openapi.Params{
		// 接口名称
		Action: tea.String(action),
		// 接口版本
		Version: tea.String("2014-08-15"),
		// 接口协议
		Protocol: tea.String("HTTPS"),
		// 接口 HTTP 方法
		Method:   tea.String("POST"),
		AuthType: tea.String("AK"),
		Style:    tea.String("RPC"),
		// 接口 PATH
		Pathname: tea.String("/"),
		// 接口请求体内容格式
		ReqBodyType: tea.String("json"),
		// 接口响应体内容格式
		BodyType: tea.String("json"),
	}
	_result = params
	return _result
}

// 获取单个实例的慢查询日志记录
func describeSlowLogRecords(instanceId, startTime, endTime string, pageNumber, pageSize int) ([]SlowLogRecord, int, error) {
	client, err := CreateRdsClient()
	if err != nil {
		return nil, 0, fmt.Errorf("创建RDS客户端失败: %v", err)
	}

	params := CreateRdsApiInfo("DescribeSlowLogRecords")
	// query params
	queries := map[string]interface{}{}
	queries["DBInstanceId"] = tea.String(instanceId)
	queries["StartTime"] = tea.String(startTime)
	queries["EndTime"] = tea.String(endTime)
	queries["PageNumber"] = tea.Int32(int32(pageNumber))
	queries["PageSize"] = tea.Int32(int32(pageSize))
	// 可选参数
	// queries["SQLId"] = tea.Int64(0) // 可选，SQL ID
	// queries["DBName"] = tea.String("") // 可选，数据库名称

	// runtime options
	runtime := &util.RuntimeOptions{}
	request := &openapi.OpenApiRequest{
		Query: openapiutil.Query(queries),
	}

	// 调用API
	resp, err := client.CallApi(params, request, runtime)
	if err != nil {
		// 检查错误类型
		if strings.Contains(err.Error(), "IncorrectDBInstanceState") {
			return nil, 0, fmt.Errorf("实例状态不正确，无法执行操作: %v", err)
		} else if strings.Contains(err.Error(), "InvalidDBInstanceId.NotFound") {
			return nil, 0, fmt.Errorf("指定的实例不存在: %v", err)
		} else if strings.Contains(err.Error(), "InvalidEndTime.Format") {
			return nil, 0, fmt.Errorf("结束时间格式错误: %v", err)
		} else if strings.Contains(err.Error(), "InvalidStartTime.Format") {
			return nil, 0, fmt.Errorf("开始时间格式错误: %v", err)
		} else if strings.Contains(err.Error(), "InvalidStartTime.Malformed") {
			return nil, 0, fmt.Errorf("开始时间格式错误: %v", err)
		} else if strings.Contains(err.Error(), "InvalidEndTime.Malformed") {
			return nil, 0, fmt.Errorf("结束时间格式错误: %v", err)
		} else {
			return nil, 0, fmt.Errorf("调用DescribeSlowLogRecords API失败: %v", err)
		}
	}

	// 解析返回结果
	body := resp["body"].(map[string]interface{})

	// 检查请求ID
	requestId, _ := body["RequestId"].(string)
	logger.Debugf("DescribeSlowLogRecords API请求ID: %s", requestId)

	// 检查引擎
	engine, _ := body["Engine"].(string)
	if engine != "MySQL" {
		logger.Warnf("非MySQL引擎: %s", engine)
	}

	items, ok := body["Items"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("解析返回结果失败: Items字段不存在")
	}

	sqlSlowRecords, ok := items["SQLSlowRecord"].([]interface{})
	if !ok {
		// 可能是空列表，返回空结果
		logger.Infof("实例 %s 没有慢查询记录", instanceId)
		return []SlowLogRecord{}, 0, nil
	}

	// 获取总记录数
	totalRecordCount := 0
	if count, ok := body["TotalRecordCount"].(float64); ok {
		totalRecordCount = int(count)
	}

	// 获取页码信息
	pageNum := 1
	if pn, ok := body["PageNumber"].(float64); ok {
		pageNum = int(pn)
	}

	pageSizeReturned := 30 // 默认值
	if ps, ok := body["PageSize"].(float64); ok {
		pageSizeReturned = int(ps)
	}

	logger.Debugf("慢查询日志分页信息: 页码=%d, 每页记录数=%d, 总记录数=%d",
		pageNum, pageSizeReturned, totalRecordCount)

	// 解析慢查询记录
	records := make([]SlowLogRecord, 0, len(sqlSlowRecords))
	for _, record := range sqlSlowRecords {
		r, ok := record.(map[string]interface{})
		if !ok {
			continue
		}

		slowLog := SlowLogRecord{}

		// 根据阿里云文档的 SQLSlowRecord 结构解析字段
		if v, ok := r["ApplicationName"].(string); ok {
			slowLog.ApplicationName = v
		}
		if v, ok := r["ClientHostName"].(string); ok {
			slowLog.ClientHostName = v
		}
		if v, ok := r["CpuTime"].(float64); ok {
			slowLog.CpuTime = int64(v)
		}
		if v, ok := r["DBName"].(string); ok {
			slowLog.DBName = v
		}
		if v, ok := r["ExecutionStartTime"].(string); ok {
			slowLog.ExecutionStartTime = v
		}
		if v, ok := r["HostAddress"].(string); ok {
			slowLog.HostAddress = v
		}
		if v, ok := r["LastRowsAffectedCount"].(float64); ok {
			slowLog.LastRowsAffectedCount = int64(v)
		}
		if v, ok := r["LockTimes"].(float64); ok {
			slowLog.LockTimes = int64(v)
		}
		if v, ok := r["LogicalIORead"].(float64); ok {
			slowLog.LogicalIORead = int64(v)
		}
		if v, ok := r["ParseRowCounts"].(float64); ok {
			slowLog.ParseRowCounts = int64(v)
		}
		if v, ok := r["PhysicalIORead"].(float64); ok {
			slowLog.PhysicalIORead = int64(v)
		}
		if v, ok := r["QueryTimeMS"].(float64); ok {
			slowLog.QueryTimeMS = int64(v)
		}
		if v, ok := r["QueryTimes"].(float64); ok {
			slowLog.QueryTimes = int64(v)
		}
		if v, ok := r["ReturnRowCounts"].(float64); ok {
			slowLog.ReturnRowCounts = int64(v)
		}
		if v, ok := r["RowsAffectedCount"].(float64); ok {
			slowLog.RowsAffectedCount = int64(v)
		}
		if v, ok := r["SQLHash"].(string); ok {
			slowLog.SQLHash = v
		}
		if v, ok := r["SQLText"].(string); ok {
			slowLog.SQLText = v
		}
		if v, ok := r["UserName"].(string); ok {
			slowLog.UserName = v
		}
		if v, ok := r["WriteIOCount"].(float64); ok {
			slowLog.WriteIOCount = int64(v)
		}

		records = append(records, slowLog)
	}

	return records, totalRecordCount, nil
}

// 获取所有慢查询日志记录（处理分页）
func GetAllSlowLogRecords(instanceId, startTime, endTime string, pageSize int) ([]SlowLogRecord, error) {
	var allRecords []SlowLogRecord
	pageNumber := 1

	for {
		records, totalCount, err := describeSlowLogRecords(instanceId, startTime, endTime, pageNumber, pageSize)
		if err != nil {
			return nil, err
		}

		allRecords = append(allRecords, records...)

		// 判断是否已获取所有记录
		if len(allRecords) >= totalCount || len(records) == 0 {
			break
		}

		pageNumber++
	}

	return allRecords, nil
}

// 获取RDS实例列表（支持分页）
func DescribeDBInstances(regionId string, instanceType string, pageNumber, pageSize int) ([]DBInstance, int, error) {
	client, err := CreateRdsClient()
	if err != nil {
		return nil, 0, fmt.Errorf("创建RDS客户端失败: %v", err)
	}

	params := CreateRdsApiInfo("DescribeDBInstances")
	// query params
	queries := map[string]interface{}{}
	queries["Engine"] = tea.String("MySQL")
	queries["DBInstanceStatus"] = tea.String("Running")
	if instanceType != "" {
		queries["DBInstanceType"] = tea.String(instanceType)
	}
	queries["RegionId"] = tea.String(regionId)
	queries["PageNumber"] = tea.Int32(int32(pageNumber))
	queries["PageSize"] = tea.Int32(int32(pageSize))

	// 可选参数
	// queries["ResourceGroupId"] = tea.String("") // 可选，资源组ID
	// queries["InstanceNetworkType"] = tea.String("") // 可选，实例网络类型
	// queries["ConnectionMode"] = tea.String("") // 可选，实例连接模式
	// queries["Tags"] = tea.String("") // 可选，标签
	// queries["VSwitchId"] = tea.String("") // 可选，交换机ID
	// queries["VpcId"] = tea.String("") // 可选，VPC ID
	// queries["ZoneId"] = tea.String("") // 可选，可用区ID
	// queries["PayType"] = tea.String("") // 可选，付费类型

	// runtime options
	runtime := &util.RuntimeOptions{}
	request := &openapi.OpenApiRequest{
		Query: openapiutil.Query(queries),
	}

	// 调用API
	resp, err := client.CallApi(params, request, runtime)
	if err != nil {
		// 检查错误类型
		if strings.Contains(err.Error(), "InvalidRegionId.NotFound") {
			return nil, 0, fmt.Errorf("指定的地域不存在: %v", err)
		} else if strings.Contains(err.Error(), "InvalidParameter") {
			return nil, 0, fmt.Errorf("参数错误: %v", err)
		} else {
			return nil, 0, fmt.Errorf("调用DescribeDBInstances API失败: %v", err)
		}
	}

	// 解析返回结果
	body := resp["body"].(map[string]interface{})

	// 检查请求ID
	requestId, _ := body["RequestId"].(string)
	logger.Debugf("DescribeDBInstances API请求ID: %s", requestId)

	items, ok := body["Items"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("解析返回结果失败: Items字段不存在")
	}

	dbInstances, ok := items["DBInstance"].([]interface{})
	if !ok {
		// 可能是空列表，返回空结果
		logger.Infof("区域 %s 没有符合条件的RDS实例", regionId)
		return []DBInstance{}, 0, nil
	}

	// 获取总记录数
	totalCount := 0
	if count, ok := body["TotalRecordCount"].(float64); ok {
		totalCount = int(count)
	}

	// 获取页码信息
	pageNum := 1
	if pn, ok := body["PageNumber"].(float64); ok {
		pageNum = int(pn)
	}

	pageSizeReturned := 30 // 默认值
	if ps, ok := body["PageSize"].(float64); ok {
		pageSizeReturned = int(ps)
	}

	logger.Debugf("RDS实例列表分页信息: 页码=%d, 每页记录数=%d, 总记录数=%d",
		pageNum, pageSizeReturned, totalCount)

	// 解析实例信息
	instances := make([]DBInstance, 0)
	for _, instance := range dbInstances {
		i, ok := instance.(map[string]interface{})
		if !ok {
			continue
		}

		dbInstance := DBInstance{}

		if v, ok := i["DBInstanceId"].(string); ok {
			dbInstance.DBInstanceId = v
		}
		if v, ok := i["DBInstanceType"].(string); ok {
			dbInstance.DBInstanceType = v
		}
		if v, ok := i["DBInstanceStatus"].(string); ok {
			dbInstance.DBInstanceStatus = v
		}
		if v, ok := i["RegionId"].(string); ok {
			dbInstance.RegionId = v
		}
		if v, ok := i["Engine"].(string); ok {
			dbInstance.Engine = v
		}
		if v, ok := i["EngineVersion"].(string); ok {
			dbInstance.EngineVersion = v
		}
		if v, ok := i["DBInstanceClass"].(string); ok {
			dbInstance.DBInstanceClass = v
		}
		if v, ok := i["DBInstanceStorage"].(float64); ok {
			dbInstance.DBInstanceStorage = int(v)
		}
		if v, ok := i["DBInstanceNetType"].(string); ok {
			dbInstance.DBInstanceNetType = v
		}
		if v, ok := i["DBInstanceDescription"].(string); ok {
			dbInstance.DBInstanceDesc = v
		}
		if v, ok := i["ConnectionMode"].(string); ok {
			dbInstance.ConnectionMode = v
		}
		if v, ok := i["LockMode"].(string); ok {
			dbInstance.LockMode = v
		}
		if v, ok := i["Category"].(string); ok {
			dbInstance.Category = v
		}
		if v, ok := i["PayType"].(string); ok {
			dbInstance.PayType = v
		}
		if v, ok := i["ConnectionString"].(string); ok {
			dbInstance.ConnectionString = v
		}
		if v, ok := i["Port"].(string); ok {
			dbInstance.Port = v
		}
		if v, ok := i["ExpireTime"].(string); ok {
			dbInstance.ExpireTime = v
		}
		if v, ok := i["ResourceGroupId"].(string); ok {
			dbInstance.ResourceGroupId = v
		}
		if v, ok := i["DBInstanceStorageType"].(string); ok {
			dbInstance.DBInstanceStorageType = v
		}
		if v, ok := i["MaintainTime"].(string); ok {
			dbInstance.MaintainTime = v
		}
		if v, ok := i["AvailabilityValue"].(string); ok {
			dbInstance.AvailabilityValue = v
		}
		if v, ok := i["MaxIOPS"].(float64); ok {
			dbInstance.MaxIOPS = int(v)
		}
		if v, ok := i["MaxConnections"].(float64); ok {
			dbInstance.MaxConnections = int(v)
		}
		if v, ok := i["CreationTime"].(string); ok {
			dbInstance.CreationTime = v
		}

		instances = append(instances, dbInstance)
	}

	return instances, totalCount, nil
}

// 获取所有RDS实例（包括主实例和只读实例）
func GetAllDBInstances(regionId string, pageSize int, config RDSSlowLogConfig) ([]DBInstance, error) {
	var allInstances []DBInstance

	// 获取主实例
	pageNumber := 1
	for {
		instances, totalCount, err := DescribeDBInstances(regionId, "Primary", pageNumber, pageSize)
		if err != nil {
			return nil, fmt.Errorf("获取主实例列表失败: %v", err)
		}

		allInstances = append(allInstances, instances...)

		// 判断是否已获取所有记录
		if len(allInstances) >= totalCount || len(instances) == 0 {
			break
		}

		pageNumber++
	}

	// 获取只读实例
	pageNumber = 1
	var readonlyInstances []DBInstance
	for {
		instances, totalCount, err := DescribeDBInstances(regionId, "Readonly", pageNumber, pageSize)
		if err != nil {
			return nil, fmt.Errorf("获取只读实例列表失败: %v", err)
		}

		readonlyInstances = append(readonlyInstances, instances...)

		// 判断是否已获取所有记录
		if len(readonlyInstances) >= totalCount || len(instances) == 0 {
			break
		}

		pageNumber++
	}

	// 合并主实例和只读实例
	allInstances = append(allInstances, readonlyInstances...)

	return allInstances, nil
}

// 格式化时间为阿里云API要求的格式
func FormatTime(t time.Time) string {
	return t.UTC().Format("2006-01-02T15:04Z")
}

// 发送邮件
func SendEmail(config RDSSlowLogConfig, subject, body, contentType string) error {
	// 检查SMTP配置
	if config.SMTPServer == "" || config.SMTPPort == 0 || config.SMTPUser == "" || config.SMTPFrom == "" {
		return fmt.Errorf("SMTP配置不完整")
	}

	// 检查收件人
	if len(config.Recipients) == 0 {
		return fmt.Errorf("没有指定收件人")
	}

	// 构建邮件头
	header := make(map[string]string)
	header["From"] = config.SMTPFrom
	header["To"] = strings.Join(config.Recipients, ",")
	header["Subject"] = subject
	header["MIME-Version"] = "1.0"

	if contentType == "html" {
		header["Content-Type"] = "text/html; charset=UTF-8"
	} else {
		header["Content-Type"] = "text/plain; charset=UTF-8"
	}

	// 构建邮件内容
	message := ""
	for k, v := range header {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + body

	// 连接到SMTP服务器
	auth := smtp.PlainAuth("", config.SMTPUser, config.SMTPPassword, config.SMTPServer)
	addr := fmt.Sprintf("%s:%d", config.SMTPServer, config.SMTPPort)

	// 发送邮件
	err := smtp.SendMail(addr, auth, config.SMTPFrom, config.Recipients, []byte(message))
	if err != nil {
		return fmt.Errorf("发送邮件失败: %v", err)
	}

	return nil
}

// 收集单个实例的慢查询日志
func CollectInstanceSlowLogs(instance DBInstance, startTime, endTime string, pageSize int) ([]SlowLogRecord, error) {
	logger.Infof("正在收集实例 %s 的慢查询日志...", instance.DBInstanceId)

	records, err := GetAllSlowLogRecords(instance.DBInstanceId, startTime, endTime, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取实例 %s 的慢查询日志失败: %v", instance.DBInstanceId, err)
	}

	logger.Infof("实例 %s 共收集到 %d 条慢查询记录", instance.DBInstanceId, len(records))
	return records, nil
}

// 主函数：获取RDS慢查询日志
func CollectRDSSlowLogs(config RDSSlowLogConfig) (map[string][]SlowLogRecord, error) {
	logger.Info("开始收集RDS慢查询日志...")

	// 验证参数
	if err := ValidateAPIParams(config); err != nil {
		return nil, fmt.Errorf("参数验证失败: %v", err)
	}

	// 计算查询时间范围
	now := time.Now()
	// 默认获取前一天的数据
	yesterday := now.AddDate(0, 0, -1)
	startTime := FormatTime(time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location()))
	endTime := FormatTime(time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 23, 59, 59, 0, yesterday.Location()))

	logger.Infof("查询时间范围: %s 至 %s", startTime, endTime)

	// 获取所有RDS实例
	instances, err := GetAllDBInstances(config.RegionId, config.PageSize, config)
	if err != nil {
		return nil, fmt.Errorf("获取RDS实例列表失败: %v", err)
	}

	if len(instances) == 0 {
		logger.Info("未找到任何RDS实例")
		return make(map[string][]SlowLogRecord), nil
	}

	logger.Infof("找到 %d 个RDS实例", len(instances))

	// 收集所有实例的慢查询日志
	instanceLogs := make(map[string][]SlowLogRecord)
	successCount := 0
	failCount := 0
	ignoredCount := 0

	// 创建忽略实例的映射，便于快速查找
	ignoreMap := make(map[string]bool)
	for _, id := range config.IgnoreDBInstanceID {
		ignoreMap[id] = true
	}

	for _, instance := range instances {
		// 检查是否在忽略列表中
		if ignoreMap[instance.DBInstanceId] {
			logger.Infof("实例 %s 在忽略列表中，跳过", instance.DBInstanceId)
			ignoredCount++
			continue
		}

		records, err := CollectInstanceSlowLogs(instance, startTime, endTime, config.PageSize)
		if err != nil {
			logger.Warnf("获取实例 %s 的慢查询日志失败: %v", instance.DBInstanceId, err)
			failCount++
			continue
		}
		instanceLogs[instance.DBInstanceId] = records
		successCount++
	}

	logger.Infof("成功收集 %d 个实例的慢查询日志，失败 %d 个，忽略 %d 个", successCount, failCount, ignoredCount)

	// 统计总的慢查询记录数
	totalSlowQueries := 0
	for _, records := range instanceLogs {
		totalSlowQueries += len(records)
	}

	logger.Infof("RDS慢查询日志收集完成，共收集到 %d 条慢查询记录", totalSlowQueries)

	return instanceLogs, nil
}

// 验证API参数
func ValidateAPIParams(config RDSSlowLogConfig) error {
	// 验证区域ID
	if config.RegionId == "" {
		return fmt.Errorf("区域ID不能为空")
	}

	// 验证查询时间范围
	if config.QueryHoursAgo <= 0 {
		return fmt.Errorf("查询时间范围必须大于0小时")
	}

	// 验证慢查询阈值
	if config.SlowThreshold <= 0 {
		return fmt.Errorf("慢查询阈值必须大于0毫秒")
	}

	// 验证分页大小
	if config.PageSize <= 0 || config.PageSize > 100 {
		return fmt.Errorf("分页大小必须在1-100之间")
	}

	// 验证收件人
	if len(config.Recipients) == 0 {
		return fmt.Errorf("至少需要一个邮件接收人")
	}

	return nil
}

// 创建默认配置
func CreateDefaultRDSSlowLogConfig() RDSSlowLogConfig {
	return RDSSlowLogConfig{
		Recipients:         []string{"<EMAIL>"},
		QueryHoursAgo:      24,
		SlowThreshold:      1000,
		RegionId:           "cn-hangzhou",
		PageSize:           100,
		IgnoreDBInstanceID: []string{"rm-bp111s3k9m2o06u6j", "rm-bp1o78g1nf9kt6rhc"}, // 默认忽略测试和开发实例
		SMTPServer:         "smtp.example.com",
		SMTPPort:           25,
		SMTPUser:           "<EMAIL>",
		SMTPPassword:       "password",
		SMTPFrom:           "<EMAIL>",
	}
}

// 运行RDS慢查询日志收集器
func RunRDSSlowLogCollector() error {
	logger.Info("开始运行RDS慢查询日志收集器...")
	config := CreateDefaultRDSSlowLogConfig()

	// 收集慢查询日志
	instanceLogs, err := CollectRDSSlowLogs(config)
	if err != nil {
		return fmt.Errorf("收集慢查询日志失败: %v", err)
	}

	// 如果没有慢查询日志，提前返回
	if len(instanceLogs) == 0 {
		logger.Info("没有慢查询日志")
		return nil
	}

	// 统计总的慢查询记录数
	totalSlowQueries := 0
	for instanceId, records := range instanceLogs {
		logger.Infof("实例 %s: %d 条慢查询记录", instanceId, len(records))
		totalSlowQueries += len(records)
	}

	logger.Infof("RDS慢查询日志收集完成，共收集到 %d 条慢查询记录", totalSlowQueries)
	return nil
}

// 设置定时任务
func SetupRDSSlowLogCronJob(config RDSSlowLogConfig, cronExpression string) {
	logger.Infof("设置RDS慢查询日志收集定时任务: %s", cronExpression)
	// 这里可以使用cron库来设置定时任务
	// 例如：
	// c := cron.New()
	// c.AddFunc(cronExpression, func() {
	//     err := RunRDSSlowLogCollector(config)
	//     if err != nil {
	//         logger.Errorf("运行RDS慢查询日志收集器失败: %v", err)
	//     }
	// })
	// c.Start()
}
