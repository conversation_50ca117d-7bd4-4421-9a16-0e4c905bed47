package ali

import (
	"fmt"
	"net/smtp"
	"os"
	"strings"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	openapiutil "github.com/alibabacloud-go/openapi-util/service"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
)

// RDS慢查询日志配置
type RDSSlowLogConfig struct {
	// 邮件接收人
	Recipients []string
	// 查询时间范围（小时）
	QueryHoursAgo int
	// 慢查询阈值（毫秒）
	SlowThreshold int
	// 区域ID
	RegionId string
	// 分页大小
	PageSize int
	// 需要忽略的实例
	IgnoreDBInstanceID []string
	// SMTP配置
	SMTPServer   string
	SMTPPort     int
	SMTPUser     string
	SMTPPassword string
	SMTPFrom     string
}

// RDS实例信息
type DBInstance struct {
	DBInstanceId          string // 实例ID
	DBInstanceType        string // 实例类型：Primary（主实例）、Readonly（只读实例）、Guard（灾备实例）、Temp（临时实例）
	DBInstanceStatus      string // 实例状态：Creating、Running、Deleting、Rebooting等
	RegionId              string // 地域ID
	Engine                string // 数据库类型
	EngineVersion         string // 数据库版本
	DBInstanceClass       string // 实例规格
	DBInstanceStorage     int    // 实例存储空间，单位：GB
	DBInstanceNetType     string // 实例的网络连接类型：Internet、Intranet
	DBInstanceDesc        string // 实例描述
	ConnectionMode        string // 实例的访问模式：Standard、Safe
	LockMode              string // 实例的锁定状态
	Category              string // 实例系列：Basic（基础版）、HighAvailability（高可用版）、AlwaysOn（集群版）、Finance（金融版）
	PayType               string // 实例的付费类型：Postpaid（按量付费）、Prepaid（包年包月）
	ConnectionString      string // 实例的内网连接地址
	Port                  string // 实例的端口地址
	ExpireTime            string // 实例到期时间，按量付费实例无到期时间
	ResourceGroupId       string // 资源组ID
	DBInstanceStorageType string // 实例存储类型：local_ssd、cloud_ssd、cloud_essd
	MaintainTime          string // 实例可维护时间段
	AvailabilityValue     string // 实例在一个月内的可用性
	MaxIOPS               int    // 最大每秒IO请求次数
	MaxConnections        int    // 最大连接数
	CreationTime          string // 创建时间
}

// 慢查询日志记录
// 根据阿里云RDS MySQL版API文档：https://help.aliyun.com/zh/rds/apsaradb-rds-for-mysql/api-rds-2014-08-15-describeslowlogrecords-mysql
type SlowLogRecord struct {
	HostAddress              string // 连接数据库的客户端地址
	DBName                   string // 数据库名称
	SQLText                  string // SQL语句
	QueryTimes               int64  // 执行次数
	LockTimes                int64  // 锁定时间，单位：秒
	ParseRowCounts           int64  // 解析行数
	ReturnRowCounts          int64  // 返回行数
	ExecutionStartTime       string // 执行开始时间
	ExecutionEndTime         string // 执行结束时间
	MySQLTotalExecutionTimes int64  // SQL语句执行的总时间，单位：微秒
	MaxExecutionTime         int64  // 最大执行时长，单位：秒
	TotalLockTimes           int64  // 总锁定时间，单位：秒
	MaxLockTime              int64  // 最大锁定时长，单位：秒
	ParseMaxRowCount         int64  // 最大解析行数
	ParseTotalRowCounts      int64  // 总解析行数
	ReturnMaxRowCount        int64  // 最大返回行数
	ReturnTotalRowCounts     int64  // 总返回行数
	ThreadID                 string // 线程ID
	SQLExecTimeMS            int64  // SQL执行时间，单位：毫秒
	SQLExecStartTime         string // SQL执行开始时间
	SQLExecEndTime           string // SQL执行结束时间
	TotalKeysScanned         int64  // 扫描的键总数
	TotalRowsExamined        int64  // 检查的行总数
	TotalRowsReturned        int64  // 返回的行总数
	ClientPort               int    // 客户端端口
	UserName                 string // 用户名
	ClientIPAddress          string // 客户端IP地址
	LogicalReadBytes         int64  // 逻辑读取字节数
	PhysicalReadBytes        int64  // 物理读取字节数
	ResultSize               int64  // 结果集大小
	NetWaitTime              int64  // 网络等待时间，单位：微秒
	CPUTime                  int64  // CPU时间，单位：微秒
	IOWaitTime               int64  // IO等待时间，单位：微秒
	ApplicationWaitTime      int64  // 应用等待时间，单位：微秒
	CommitTime               int64  // 提交时间，单位：微秒
	GetLockTime              int64  // 获取锁时间，单位：微秒
	QueueTime                int64  // 队列时间，单位：微秒
	ElapsedTime              int64  // 总耗时，单位：微秒
}

// 慢查询统计信息
type SlowLogStat struct {
	SQLText            string  // SQL语句
	Count              int     // 执行次数
	AvgExecutionTime   float64 // 平均执行时间，单位：微秒
	MaxExecutionTime   int64   // 最大执行时间，单位：微秒
	TotalExecutionTime int64   // 总执行时间，单位：微秒
	AvgLockTime        float64 // 平均锁定时间，单位：秒
	MaxLockTime        int64   // 最大锁定时间，单位：秒
	AvgParseRows       float64 // 平均解析行数
	MaxParseRows       int64   // 最大解析行数
	AvgReturnRows      float64 // 平均返回行数
	MaxReturnRows      int64   // 最大返回行数
	DBNames            string  // 数据库名称列表，逗号分隔
	HostAddresses      string  // 客户端地址列表，逗号分隔
	LastExecuteTime    string  // 最后执行时间

	// 新增字段
	AvgKeysScanned  float64 // 平均扫描键数
	MaxKeysScanned  int64   // 最大扫描键数
	AvgRowsExamined float64 // 平均检查行数
	MaxRowsExamined int64   // 最大检查行数
	AvgLogicalRead  float64 // 平均逻辑读取字节数
	MaxLogicalRead  int64   // 最大逻辑读取字节数
	AvgPhysicalRead float64 // 平均物理读取字节数
	MaxPhysicalRead int64   // 最大物理读取字节数
	AvgResultSize   float64 // 平均结果集大小
	MaxResultSize   int64   // 最大结果集大小
	AvgNetWaitTime  float64 // 平均网络等待时间（微秒）
	MaxNetWaitTime  int64   // 最大网络等待时间（微秒）
	AvgCPUTime      float64 // 平均CPU时间（微秒）
	MaxCPUTime      int64   // 最大CPU时间（微秒）
	AvgIOWaitTime   float64 // 平均IO等待时间（微秒）
	MaxIOWaitTime   int64   // 最大IO等待时间（微秒）
	UserNames       string  // 用户名列表，逗号分隔
}

// 实例慢查询报告
type InstanceSlowLogReport struct {
	Instance   DBInstance
	SlowLogs   []SlowLogStat
	TotalCount int
}

// 全局慢查询报告
type SlowLogReport struct {
	StartTime       string
	EndTime         string
	InstanceReports []InstanceSlowLogReport
	GeneratedAt     string
}

// @return OpenApi.Params
func CreateRdsApiInfo(action string) (_result *openapi.Params) {
	params := &openapi.Params{
		// 接口名称
		Action: tea.String(action),
		// 接口版本
		Version: tea.String("2014-08-15"),
		// 接口协议
		Protocol: tea.String("HTTPS"),
		// 接口 HTTP 方法
		Method:   tea.String("POST"),
		AuthType: tea.String("AK"),
		Style:    tea.String("RPC"),
		// 接口 PATH
		Pathname: tea.String("/"),
		// 接口请求体内容格式
		ReqBodyType: tea.String("json"),
		// 接口响应体内容格式
		BodyType: tea.String("json"),
	}
	_result = params
	return _result
}

// 获取单个实例的慢查询日志记录
func describeSlowLogRecords(instanceId, startTime, endTime string, pageNumber, pageSize int) ([]SlowLogRecord, int, error) {
	client, err := CreateRdsClient()
	if err != nil {
		return nil, 0, fmt.Errorf("创建RDS客户端失败: %v", err)
	}

	params := CreateRdsApiInfo("DescribeSlowLogRecords")
	// query params
	queries := map[string]interface{}{}
	queries["DBInstanceId"] = tea.String(instanceId)
	queries["StartTime"] = tea.String(startTime)
	queries["EndTime"] = tea.String(endTime)
	queries["PageNumber"] = tea.Int32(int32(pageNumber))
	queries["PageSize"] = tea.Int32(int32(pageSize))
	// 可选参数
	// queries["SQLId"] = tea.Int64(0) // 可选，SQL ID
	// queries["DBName"] = tea.String("") // 可选，数据库名称

	// runtime options
	runtime := &util.RuntimeOptions{}
	request := &openapi.OpenApiRequest{
		Query: openapiutil.Query(queries),
	}

	// 调用API
	resp, err := client.CallApi(params, request, runtime)
	if err != nil {
		// 检查错误类型
		if strings.Contains(err.Error(), "IncorrectDBInstanceState") {
			return nil, 0, fmt.Errorf("实例状态不正确，无法执行操作: %v", err)
		} else if strings.Contains(err.Error(), "InvalidDBInstanceId.NotFound") {
			return nil, 0, fmt.Errorf("指定的实例不存在: %v", err)
		} else if strings.Contains(err.Error(), "InvalidEndTime.Format") {
			return nil, 0, fmt.Errorf("结束时间格式错误: %v", err)
		} else if strings.Contains(err.Error(), "InvalidStartTime.Format") {
			return nil, 0, fmt.Errorf("开始时间格式错误: %v", err)
		} else if strings.Contains(err.Error(), "InvalidStartTime.Malformed") {
			return nil, 0, fmt.Errorf("开始时间格式错误: %v", err)
		} else if strings.Contains(err.Error(), "InvalidEndTime.Malformed") {
			return nil, 0, fmt.Errorf("结束时间格式错误: %v", err)
		} else {
			return nil, 0, fmt.Errorf("调用DescribeSlowLogRecords API失败: %v", err)
		}
	}

	// 解析返回结果
	body := resp["body"].(map[string]interface{})

	// 检查请求ID
	requestId, _ := body["RequestId"].(string)
	logger.Debugf("DescribeSlowLogRecords API请求ID: %s", requestId)

	// 检查引擎
	engine, _ := body["Engine"].(string)
	if engine != "MySQL" {
		logger.Warnf("非MySQL引擎: %s", engine)
	}

	items, ok := body["Items"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("解析返回结果失败: Items字段不存在")
	}

	sqlSlowRecords, ok := items["SQLSlowRecord"].([]interface{})
	if !ok {
		// 可能是空列表，返回空结果
		logger.Infof("实例 %s 没有慢查询记录", instanceId)
		return []SlowLogRecord{}, 0, nil
	}

	// 获取总记录数
	totalRecordCount := 0
	if count, ok := body["TotalRecordCount"].(float64); ok {
		totalRecordCount = int(count)
	}

	// 获取页码信息
	pageNum := 1
	if pn, ok := body["PageNumber"].(float64); ok {
		pageNum = int(pn)
	}

	pageSizeReturned := 30 // 默认值
	if ps, ok := body["PageSize"].(float64); ok {
		pageSizeReturned = int(ps)
	}

	logger.Debugf("慢查询日志分页信息: 页码=%d, 每页记录数=%d, 总记录数=%d",
		pageNum, pageSizeReturned, totalRecordCount)

	// 解析慢查询记录
	records := make([]SlowLogRecord, 0, len(sqlSlowRecords))
	for _, record := range sqlSlowRecords {
		r, ok := record.(map[string]interface{})
		if !ok {
			continue
		}

		slowLog := SlowLogRecord{}

		if v, ok := r["DBName"].(string); ok {
			slowLog.DBName = v
		}
		if v, ok := r["SQLText"].(string); ok {
			slowLog.SQLText = v
		}
		if v, ok := r["MySQLTotalExecutionTimes"].(float64); ok {
			slowLog.MySQLTotalExecutionTimes = int64(v)
		}
		if v, ok := r["TotalLockTimes"].(float64); ok {
			slowLog.TotalLockTimes = int64(v)
		}
		if v, ok := r["LockTimes"].(float64); ok {
			slowLog.LockTimes = int64(v)
		}
		if v, ok := r["ParseTotalRowCounts"].(float64); ok {
			slowLog.ParseTotalRowCounts = int64(v)
		}
		if v, ok := r["ParseRowCounts"].(float64); ok {
			slowLog.ParseRowCounts = int64(v)
		}
		if v, ok := r["ReturnRowCounts"].(float64); ok {
			slowLog.ReturnRowCounts = int64(v)
		}
		if v, ok := r["ReturnTotalRowCounts"].(float64); ok {
			slowLog.ReturnTotalRowCounts = int64(v)
		}
		if v, ok := r["QueryTimes"].(float64); ok {
			slowLog.QueryTimes = int64(v)
		}
		if v, ok := r["MaxExecutionTime"].(float64); ok {
			slowLog.MaxExecutionTime = int64(v)
		}
		if v, ok := r["MaxLockTime"].(float64); ok {
			slowLog.MaxLockTime = int64(v)
		}
		if v, ok := r["ParseMaxRowCount"].(float64); ok {
			slowLog.ParseMaxRowCount = int64(v)
		}
		if v, ok := r["ReturnMaxRowCount"].(float64); ok {
			slowLog.ReturnMaxRowCount = int64(v)
		}
		if v, ok := r["ExecutionStartTime"].(string); ok {
			slowLog.ExecutionStartTime = v
		}
		if v, ok := r["HostAddress"].(string); ok {
			slowLog.HostAddress = v
		}
		if v, ok := r["DBName"].(string); ok {
			slowLog.DBName = v
		}

		records = append(records, slowLog)
	}

	return records, totalRecordCount, nil
}

// 获取所有慢查询日志记录（处理分页）
func GetAllSlowLogRecords(instanceId, startTime, endTime string, pageSize int) ([]SlowLogRecord, error) {
	var allRecords []SlowLogRecord
	pageNumber := 1

	for {
		records, totalCount, err := describeSlowLogRecords(instanceId, startTime, endTime, pageNumber, pageSize)
		if err != nil {
			return nil, err
		}

		allRecords = append(allRecords, records...)

		// 判断是否已获取所有记录
		if len(allRecords) >= totalCount || len(records) == 0 {
			break
		}

		pageNumber++
	}

	return allRecords, nil
}

// 获取RDS实例列表（支持分页）
func DescribeDBInstances(regionId string, instanceType string, pageNumber, pageSize int) ([]DBInstance, int, error) {
	client, err := CreateRdsClient()
	if err != nil {
		return nil, 0, fmt.Errorf("创建RDS客户端失败: %v", err)
	}

	params := CreateRdsApiInfo("DescribeDBInstances")
	// query params
	queries := map[string]interface{}{}
	queries["Engine"] = tea.String("MySQL")
	queries["DBInstanceStatus"] = tea.String("Running")
	if instanceType != "" {
		queries["DBInstanceType"] = tea.String(instanceType)
	}
	queries["RegionId"] = tea.String(regionId)
	queries["PageNumber"] = tea.Int32(int32(pageNumber))
	queries["PageSize"] = tea.Int32(int32(pageSize))

	// 可选参数
	// queries["ResourceGroupId"] = tea.String("") // 可选，资源组ID
	// queries["InstanceNetworkType"] = tea.String("") // 可选，实例网络类型
	// queries["ConnectionMode"] = tea.String("") // 可选，实例连接模式
	// queries["Tags"] = tea.String("") // 可选，标签
	// queries["VSwitchId"] = tea.String("") // 可选，交换机ID
	// queries["VpcId"] = tea.String("") // 可选，VPC ID
	// queries["ZoneId"] = tea.String("") // 可选，可用区ID
	// queries["PayType"] = tea.String("") // 可选，付费类型

	// runtime options
	runtime := &util.RuntimeOptions{}
	request := &openapi.OpenApiRequest{
		Query: openapiutil.Query(queries),
	}

	// 调用API
	resp, err := client.CallApi(params, request, runtime)
	if err != nil {
		// 检查错误类型
		if strings.Contains(err.Error(), "InvalidRegionId.NotFound") {
			return nil, 0, fmt.Errorf("指定的地域不存在: %v", err)
		} else if strings.Contains(err.Error(), "InvalidParameter") {
			return nil, 0, fmt.Errorf("参数错误: %v", err)
		} else {
			return nil, 0, fmt.Errorf("调用DescribeDBInstances API失败: %v", err)
		}
	}

	// 解析返回结果
	body := resp["body"].(map[string]interface{})

	// 检查请求ID
	requestId, _ := body["RequestId"].(string)
	logger.Debugf("DescribeDBInstances API请求ID: %s", requestId)

	items, ok := body["Items"].(map[string]interface{})
	if !ok {
		return nil, 0, fmt.Errorf("解析返回结果失败: Items字段不存在")
	}

	dbInstances, ok := items["DBInstance"].([]interface{})
	if !ok {
		// 可能是空列表，返回空结果
		logger.Infof("区域 %s 没有符合条件的RDS实例", regionId)
		return []DBInstance{}, 0, nil
	}

	// 获取总记录数
	totalCount := 0
	if count, ok := body["TotalRecordCount"].(float64); ok {
		totalCount = int(count)
	}

	// 获取页码信息
	pageNum := 1
	if pn, ok := body["PageNumber"].(float64); ok {
		pageNum = int(pn)
	}

	pageSizeReturned := 30 // 默认值
	if ps, ok := body["PageSize"].(float64); ok {
		pageSizeReturned = int(ps)
	}

	logger.Debugf("RDS实例列表分页信息: 页码=%d, 每页记录数=%d, 总记录数=%d",
		pageNum, pageSizeReturned, totalCount)

	// 解析实例信息
	instances := make([]DBInstance, 0)
	for _, instance := range dbInstances {
		i, ok := instance.(map[string]interface{})
		if !ok {
			continue
		}

		dbInstance := DBInstance{}

		if v, ok := i["DBInstanceId"].(string); ok {
			dbInstance.DBInstanceId = v
		}
		if v, ok := i["DBInstanceType"].(string); ok {
			dbInstance.DBInstanceType = v
		}
		if v, ok := i["DBInstanceStatus"].(string); ok {
			dbInstance.DBInstanceStatus = v
		}
		if v, ok := i["RegionId"].(string); ok {
			dbInstance.RegionId = v
		}
		if v, ok := i["Engine"].(string); ok {
			dbInstance.Engine = v
		}
		if v, ok := i["EngineVersion"].(string); ok {
			dbInstance.EngineVersion = v
		}
		if v, ok := i["DBInstanceClass"].(string); ok {
			dbInstance.DBInstanceClass = v
		}
		if v, ok := i["DBInstanceStorage"].(float64); ok {
			dbInstance.DBInstanceStorage = int(v)
		}
		if v, ok := i["DBInstanceNetType"].(string); ok {
			dbInstance.DBInstanceNetType = v
		}
		if v, ok := i["DBInstanceDescription"].(string); ok {
			dbInstance.DBInstanceDesc = v
		}
		if v, ok := i["ConnectionMode"].(string); ok {
			dbInstance.ConnectionMode = v
		}
		if v, ok := i["LockMode"].(string); ok {
			dbInstance.LockMode = v
		}
		if v, ok := i["Category"].(string); ok {
			dbInstance.Category = v
		}
		if v, ok := i["PayType"].(string); ok {
			dbInstance.PayType = v
		}
		if v, ok := i["ConnectionString"].(string); ok {
			dbInstance.ConnectionString = v
		}
		if v, ok := i["Port"].(string); ok {
			dbInstance.Port = v
		}
		if v, ok := i["ExpireTime"].(string); ok {
			dbInstance.ExpireTime = v
		}
		if v, ok := i["ResourceGroupId"].(string); ok {
			dbInstance.ResourceGroupId = v
		}
		if v, ok := i["DBInstanceStorageType"].(string); ok {
			dbInstance.DBInstanceStorageType = v
		}
		if v, ok := i["MaintainTime"].(string); ok {
			dbInstance.MaintainTime = v
		}
		if v, ok := i["AvailabilityValue"].(string); ok {
			dbInstance.AvailabilityValue = v
		}
		if v, ok := i["MaxIOPS"].(float64); ok {
			dbInstance.MaxIOPS = int(v)
		}
		if v, ok := i["MaxConnections"].(float64); ok {
			dbInstance.MaxConnections = int(v)
		}
		if v, ok := i["CreationTime"].(string); ok {
			dbInstance.CreationTime = v
		}

		instances = append(instances, dbInstance)
	}

	return instances, totalCount, nil
}

// 获取所有RDS实例（包括主实例和只读实例）
func GetAllDBInstances(regionId string, pageSize int, config RDSSlowLogConfig) ([]DBInstance, error) {
	var allInstances []DBInstance

	// 获取主实例
	pageNumber := 1
	for {
		instances, totalCount, err := DescribeDBInstances(regionId, "Primary", pageNumber, pageSize)
		if err != nil {
			return nil, fmt.Errorf("获取主实例列表失败: %v", err)
		}

		allInstances = append(allInstances, instances...)

		// 判断是否已获取所有记录
		if len(allInstances) >= totalCount || len(instances) == 0 {
			break
		}

		pageNumber++
	}

	// 获取只读实例
	pageNumber = 1
	var readonlyInstances []DBInstance
	for {
		instances, totalCount, err := DescribeDBInstances(regionId, "Readonly", pageNumber, pageSize)
		if err != nil {
			return nil, fmt.Errorf("获取只读实例列表失败: %v", err)
		}

		readonlyInstances = append(readonlyInstances, instances...)

		// 判断是否已获取所有记录
		if len(readonlyInstances) >= totalCount || len(instances) == 0 {
			break
		}

		pageNumber++
	}

	// 合并主实例和只读实例
	allInstances = append(allInstances, readonlyInstances...)

	return allInstances, nil
}

// 处理慢查询日志数据，生成统计信息
func ProcessSlowLogData(instancesLogs map[string][]SlowLogRecord, instances []DBInstance) []InstanceSlowLogReport {
	// 创建实例ID到实例信息的映射
	instanceMap := make(map[string]DBInstance)
	for _, instance := range instances {
		instanceMap[instance.DBInstanceId] = instance
	}

	// 处理每个实例的慢查询日志
	var reports []InstanceSlowLogReport

	for instanceId, logs := range instancesLogs {
		// 跳过没有慢查询日志的实例
		if len(logs) == 0 {
			continue
		}

		// 获取实例信息
		instance, ok := instanceMap[instanceId]
		if !ok {
			// 如果找不到实例信息，创建一个只有ID的实例
			instance = DBInstance{DBInstanceId: instanceId}
		}

		// 按SQL文本分组
		sqlGroups := make(map[string][]SlowLogRecord)
		for _, log := range logs {
			sqlGroups[log.SQLText] = append(sqlGroups[log.SQLText], log)
		}

		// 生成统计信息
		var stats []SlowLogStat
		for sqlText, sqlLogs := range sqlGroups {
			stat := SlowLogStat{
				SQLText: sqlText,
				Count:   len(sqlLogs),
			}

			// 计算统计信息
			var totalTime int64
			var totalLockTime int64
			var totalParseRows int64
			var totalReturnRows int64
			var maxTime int64

			dbNames := make(map[string]bool)
			hostAddresses := make(map[string]bool)
			userNames := make(map[string]bool)
			lastExecuteTime := ""

			var maxLockTime int64
			var maxParseRows int64
			var maxReturnRows int64

			// 新增字段的统计
			var totalKeysScanned int64
			var totalRowsExamined int64
			var totalLogicalRead int64
			var totalPhysicalRead int64
			var totalResultSize int64
			var totalNetWaitTime int64
			var totalCPUTime int64
			var totalIOWaitTime int64

			var maxKeysScanned int64
			var maxRowsExamined int64
			var maxLogicalRead int64
			var maxPhysicalRead int64
			var maxResultSize int64
			var maxNetWaitTime int64
			var maxCPUTime int64
			var maxIOWaitTime int64

			for _, log := range sqlLogs {
				totalTime += log.MySQLTotalExecutionTimes
				totalLockTime += log.TotalLockTimes
				totalParseRows += log.ParseRowCounts
				totalReturnRows += log.ReturnRowCounts

				// 新增字段的累加
				totalKeysScanned += log.TotalKeysScanned
				totalRowsExamined += log.TotalRowsExamined
				totalLogicalRead += log.LogicalReadBytes
				totalPhysicalRead += log.PhysicalReadBytes
				totalResultSize += log.ResultSize
				totalNetWaitTime += log.NetWaitTime
				totalCPUTime += log.CPUTime
				totalIOWaitTime += log.IOWaitTime

				// 计算最大执行时间
				if log.MySQLTotalExecutionTimes > maxTime {
					maxTime = log.MySQLTotalExecutionTimes
				}

				// 计算最大锁定时间
				if log.MaxLockTime > maxLockTime {
					maxLockTime = log.MaxLockTime
				}

				// 计算最大解析行数
				if log.ParseMaxRowCount > maxParseRows {
					maxParseRows = log.ParseMaxRowCount
				}

				// 计算最大返回行数
				if log.ReturnMaxRowCount > maxReturnRows {
					maxReturnRows = log.ReturnMaxRowCount
				}

				// 新增字段的最大值计算
				if log.TotalKeysScanned > maxKeysScanned {
					maxKeysScanned = log.TotalKeysScanned
				}
				if log.TotalRowsExamined > maxRowsExamined {
					maxRowsExamined = log.TotalRowsExamined
				}
				if log.LogicalReadBytes > maxLogicalRead {
					maxLogicalRead = log.LogicalReadBytes
				}
				if log.PhysicalReadBytes > maxPhysicalRead {
					maxPhysicalRead = log.PhysicalReadBytes
				}
				if log.ResultSize > maxResultSize {
					maxResultSize = log.ResultSize
				}
				if log.NetWaitTime > maxNetWaitTime {
					maxNetWaitTime = log.NetWaitTime
				}
				if log.CPUTime > maxCPUTime {
					maxCPUTime = log.CPUTime
				}
				if log.IOWaitTime > maxIOWaitTime {
					maxIOWaitTime = log.IOWaitTime
				}

				if log.DBName != "" {
					dbNames[log.DBName] = true
				}

				if log.HostAddress != "" {
					hostAddresses[log.HostAddress] = true
				}

				if log.UserName != "" {
					userNames[log.UserName] = true
				}

				if log.ExecutionStartTime > lastExecuteTime {
					lastExecuteTime = log.ExecutionStartTime
				}
			}

			// 计算平均值
			count := float64(len(sqlLogs))
			stat.AvgExecutionTime = float64(totalTime) / count
			stat.MaxExecutionTime = maxTime
			stat.TotalExecutionTime = totalTime
			stat.AvgLockTime = float64(totalLockTime) / count
			stat.MaxLockTime = maxLockTime
			stat.AvgParseRows = float64(totalParseRows) / count
			stat.MaxParseRows = maxParseRows
			stat.AvgReturnRows = float64(totalReturnRows) / count
			stat.MaxReturnRows = maxReturnRows

			// 新增字段的平均值计算
			stat.AvgKeysScanned = float64(totalKeysScanned) / count
			stat.MaxKeysScanned = maxKeysScanned
			stat.AvgRowsExamined = float64(totalRowsExamined) / count
			stat.MaxRowsExamined = maxRowsExamined
			stat.AvgLogicalRead = float64(totalLogicalRead) / count
			stat.MaxLogicalRead = maxLogicalRead
			stat.AvgPhysicalRead = float64(totalPhysicalRead) / count
			stat.MaxPhysicalRead = maxPhysicalRead
			stat.AvgResultSize = float64(totalResultSize) / count
			stat.MaxResultSize = maxResultSize
			stat.AvgNetWaitTime = float64(totalNetWaitTime) / count
			stat.MaxNetWaitTime = maxNetWaitTime
			stat.AvgCPUTime = float64(totalCPUTime) / count
			stat.MaxCPUTime = maxCPUTime
			stat.AvgIOWaitTime = float64(totalIOWaitTime) / count
			stat.MaxIOWaitTime = maxIOWaitTime

			// 转换集合为字符串
			dbNamesList := make([]string, 0, len(dbNames))
			for dbName := range dbNames {
				dbNamesList = append(dbNamesList, dbName)
			}
			stat.DBNames = strings.Join(dbNamesList, ", ")

			hostList := make([]string, 0, len(hostAddresses))
			for host := range hostAddresses {
				hostList = append(hostList, host)
			}
			stat.HostAddresses = strings.Join(hostList, ", ")

			stat.LastExecuteTime = lastExecuteTime

			stats = append(stats, stat)
		}

		// 按总执行时间排序（简单冒泡排序）
		for i := 0; i < len(stats)-1; i++ {
			for j := 0; j < len(stats)-i-1; j++ {
				if stats[j].TotalExecutionTime < stats[j+1].TotalExecutionTime {
					stats[j], stats[j+1] = stats[j+1], stats[j]
				}
			}
		}

		// 创建实例报告
		report := InstanceSlowLogReport{
			Instance:   instance,
			SlowLogs:   stats,
			TotalCount: len(logs),
		}

		reports = append(reports, report)
	}

	// 按慢查询总数排序（简单冒泡排序）
	for i := 0; i < len(reports)-1; i++ {
		for j := 0; j < len(reports)-i-1; j++ {
			if reports[j].TotalCount < reports[j+1].TotalCount {
				reports[j], reports[j+1] = reports[j+1], reports[j]
			}
		}
	}

	return reports
}

// 格式化时间为阿里云API要求的格式
func FormatTime(t time.Time) string {
	return t.UTC().Format("2006-01-02T15:04Z")
}

// 生成慢查询日志报告HTML
func GenerateHTMLReport(report SlowLogReport) string {
	var sb strings.Builder

	// HTML头部
	sb.WriteString("<!DOCTYPE html>\n")
	sb.WriteString("<html>\n<head>\n")
	sb.WriteString("<meta charset=\"UTF-8\">\n")
	sb.WriteString("<title>RDS慢查询日志报告</title>\n")
	sb.WriteString("<style>\n")
	sb.WriteString("body { font-family: Arial, sans-serif; margin: 20px; }\n")
	sb.WriteString("h1, h2 { color: #333; }\n")
	sb.WriteString("table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }\n")
	sb.WriteString("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n")
	sb.WriteString("th { background-color: #f2f2f2; }\n")
	sb.WriteString("tr:nth-child(even) { background-color: #f9f9f9; }\n")
	sb.WriteString(".instance-header { background-color: #e6f2ff; padding: 10px; margin-top: 20px; border-radius: 5px; }\n")
	sb.WriteString(".sql-text { max-width: 500px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }\n")
	sb.WriteString(".sql-text:hover { white-space: normal; overflow: visible; }\n")
	sb.WriteString("</style>\n")
	sb.WriteString("</head>\n<body>\n")

	// 报告标题
	sb.WriteString("<h1>RDS慢查询日志报告</h1>\n")
	sb.WriteString("<p><strong>查询时间范围:</strong> " + report.StartTime + " 至 " + report.EndTime + "</p>\n")
	sb.WriteString("<p><strong>生成时间:</strong> " + report.GeneratedAt + "</p>\n")
	sb.WriteString("<p><strong>实例总数:</strong> " + fmt.Sprintf("%d", len(report.InstanceReports)) + "</p>\n")

	// 实例报告
	for _, instanceReport := range report.InstanceReports {
		sb.WriteString("<div class=\"instance-header\">\n")
		sb.WriteString("<h2>实例: " + instanceReport.Instance.DBInstanceId + "</h2>\n")
		sb.WriteString("<p><strong>实例类型:</strong> " + instanceReport.Instance.DBInstanceType + "</p>\n")
		sb.WriteString("<p><strong>实例描述:</strong> " + instanceReport.Instance.DBInstanceDesc + "</p>\n")
		sb.WriteString("<p><strong>数据库引擎:</strong> " + instanceReport.Instance.Engine + " " + instanceReport.Instance.EngineVersion + "</p>\n")
		sb.WriteString("<p><strong>慢查询总数:</strong> " + fmt.Sprintf("%d", instanceReport.TotalCount) + "</p>\n")
		sb.WriteString("</div>\n")

		// // 慢查询表格
		// sb.WriteString("<h3>慢查询统计</h3>\n")
		// sb.WriteString("<table>\n")
		// sb.WriteString("<tr>\n")
		// sb.WriteString("<th>序号</th>\n")
		// sb.WriteString("<th>SQL语句</th>\n")
		// sb.WriteString("<th>执行次数</th>\n")
		// sb.WriteString("<th>平均执行时间(μs)</th>\n")
		// sb.WriteString("<th>最大执行时间(μs)</th>\n")
		// sb.WriteString("<th>总执行时间(μs)</th>\n")
		// sb.WriteString("<th>平均锁定时间(秒)</th>\n")
		// sb.WriteString("<th>数据库</th>\n")
		// sb.WriteString("<th>最后执行时间</th>\n")
		// sb.WriteString("</tr>\n")

		// for i, stat := range instanceReport.SlowLogs {
		// 	sb.WriteString("<tr>\n")
		// 	sb.WriteString("<td>" + fmt.Sprintf("%d", i+1) + "</td>\n")
		// 	sb.WriteString("<td class=\"sql-text\">" + stat.SQLText + "</td>\n")
		// 	sb.WriteString("<td>" + fmt.Sprintf("%d", stat.Count) + "</td>\n")
		// 	sb.WriteString("<td>" + fmt.Sprintf("%.2f", stat.AvgExecutionTime) + "</td>\n")
		// 	sb.WriteString("<td>" + fmt.Sprintf("%d", stat.MaxExecutionTime) + "</td>\n")
		// 	sb.WriteString("<td>" + fmt.Sprintf("%d", stat.TotalExecutionTime) + "</td>\n")
		// 	sb.WriteString("<td>" + fmt.Sprintf("%.2f", stat.AvgLockTime) + "</td>\n")
		// 	sb.WriteString("<td>" + stat.DBNames + "</td>\n")
		// 	sb.WriteString("<td>" + stat.LastExecuteTime + "</td>\n")
		// 	sb.WriteString("</tr>\n")
		// }
		// sb.WriteString("</table>\n")

		// 详细信息表格
		sb.WriteString("<h3>详细信息</h3>\n")
		sb.WriteString("<table>\n")
		sb.WriteString("<tr>\n")
		sb.WriteString("<th>序号</th>\n")
		sb.WriteString("<th>SQL语句</th>\n")
		sb.WriteString("<th>最大锁定时间(秒)</th>\n")
		sb.WriteString("<th>平均解析行数</th>\n")
		sb.WriteString("<th>最大解析行数</th>\n")
		sb.WriteString("<th>平均返回行数</th>\n")
		sb.WriteString("<th>最大返回行数</th>\n")
		sb.WriteString("<th>客户端地址</th>\n")
		sb.WriteString("</tr>\n")

		for i, stat := range instanceReport.SlowLogs {
			sb.WriteString("<tr>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%d", i+1) + "</td>\n")
			sb.WriteString("<td class=\"sql-text\">" + stat.SQLText + "</td>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%d", stat.MaxLockTime) + "</td>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%.2f", stat.AvgParseRows) + "</td>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%d", stat.MaxParseRows) + "</td>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%.2f", stat.AvgReturnRows) + "</td>\n")
			sb.WriteString("<td>" + fmt.Sprintf("%d", stat.MaxReturnRows) + "</td>\n")
			sb.WriteString("<td>" + stat.HostAddresses + "</td>\n")
			sb.WriteString("</tr>\n")
		}
		sb.WriteString("</table>\n")
	}

	// HTML尾部
	sb.WriteString("</body>\n</html>")

	return sb.String()
}

// 生成慢查询日志报告文本
func GenerateTextReport(report SlowLogReport) string {
	var sb strings.Builder

	sb.WriteString("RDS慢查询日志报告\n")
	sb.WriteString("====================\n\n")
	sb.WriteString(fmt.Sprintf("查询时间范围: %s 至 %s\n", report.StartTime, report.EndTime))
	sb.WriteString(fmt.Sprintf("生成时间: %s\n", report.GeneratedAt))
	sb.WriteString(fmt.Sprintf("实例总数: %d\n\n", len(report.InstanceReports)))

	for _, instanceReport := range report.InstanceReports {
		sb.WriteString(fmt.Sprintf("实例: %s\n", instanceReport.Instance.DBInstanceId))
		sb.WriteString(fmt.Sprintf("实例类型: %s\n", instanceReport.Instance.DBInstanceType))
		sb.WriteString(fmt.Sprintf("实例描述: %s\n", instanceReport.Instance.DBInstanceDesc))
		sb.WriteString(fmt.Sprintf("数据库引擎: %s %s\n", instanceReport.Instance.Engine, instanceReport.Instance.EngineVersion))
		sb.WriteString(fmt.Sprintf("慢查询总数: %d\n\n", instanceReport.TotalCount))

		sb.WriteString("慢查询统计:\n")
		sb.WriteString("--------------------\n")
		for i, stat := range instanceReport.SlowLogs {
			sb.WriteString(fmt.Sprintf("%d. SQL: %s\n", i+1, stat.SQLText))
			sb.WriteString(fmt.Sprintf("   执行次数: %d\n", stat.Count))
			sb.WriteString(fmt.Sprintf("   平均执行时间: %.2f μs\n", stat.AvgExecutionTime))
			sb.WriteString(fmt.Sprintf("   最大执行时间: %d μs\n", stat.MaxExecutionTime))
			sb.WriteString(fmt.Sprintf("   总执行时间: %d μs\n", stat.TotalExecutionTime))
			sb.WriteString(fmt.Sprintf("   平均锁定时间: %.2f 秒\n", stat.AvgLockTime))
			sb.WriteString(fmt.Sprintf("   最大锁定时间: %d 秒\n", stat.MaxLockTime))
			sb.WriteString(fmt.Sprintf("   平均解析行数: %.2f\n", stat.AvgParseRows))
			sb.WriteString(fmt.Sprintf("   最大解析行数: %d\n", stat.MaxParseRows))
			sb.WriteString(fmt.Sprintf("   平均返回行数: %.2f\n", stat.AvgReturnRows))
			sb.WriteString(fmt.Sprintf("   最大返回行数: %d\n", stat.MaxReturnRows))
			sb.WriteString(fmt.Sprintf("   数据库: %s\n", stat.DBNames))
			sb.WriteString(fmt.Sprintf("   客户端地址: %s\n", stat.HostAddresses))
			sb.WriteString(fmt.Sprintf("   最后执行时间: %s\n\n", stat.LastExecuteTime))
		}
		sb.WriteString("\n")
	}

	return sb.String()
}

// 发送邮件
func SendEmail(config RDSSlowLogConfig, subject, body, contentType string) error {
	// 检查SMTP配置
	if config.SMTPServer == "" || config.SMTPPort == 0 || config.SMTPUser == "" || config.SMTPFrom == "" {
		return fmt.Errorf("SMTP配置不完整")
	}

	// 检查收件人
	if len(config.Recipients) == 0 {
		return fmt.Errorf("没有指定收件人")
	}

	// 构建邮件头
	header := make(map[string]string)
	header["From"] = config.SMTPFrom
	header["To"] = strings.Join(config.Recipients, ",")
	header["Subject"] = subject
	header["MIME-Version"] = "1.0"

	if contentType == "html" {
		header["Content-Type"] = "text/html; charset=UTF-8"
	} else {
		header["Content-Type"] = "text/plain; charset=UTF-8"
	}

	// 构建邮件内容
	message := ""
	for k, v := range header {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + body

	// 连接到SMTP服务器
	auth := smtp.PlainAuth("", config.SMTPUser, config.SMTPPassword, config.SMTPServer)
	addr := fmt.Sprintf("%s:%d", config.SMTPServer, config.SMTPPort)

	// 发送邮件
	err := smtp.SendMail(addr, auth, config.SMTPFrom, config.Recipients, []byte(message))
	if err != nil {
		return fmt.Errorf("发送邮件失败: %v", err)
	}

	return nil
}

// 收集单个实例的慢查询日志
func CollectInstanceSlowLogs(instance DBInstance, startTime, endTime string, pageSize int) ([]SlowLogRecord, error) {
	logger.Infof("正在收集实例 %s 的慢查询日志...", instance.DBInstanceId)

	records, err := GetAllSlowLogRecords(instance.DBInstanceId, startTime, endTime, pageSize)
	if err != nil {
		return nil, fmt.Errorf("获取实例 %s 的慢查询日志失败: %v", instance.DBInstanceId, err)
	}

	logger.Infof("实例 %s 共收集到 %d 条慢查询记录", instance.DBInstanceId, len(records))
	return records, nil
}

// 主函数：获取RDS慢查询日志并生成报告
func CollectAndGenerateRDSSlowLogs(config RDSSlowLogConfig) (string, *SlowLogReport, error) {
	logger.Info("开始收集RDS慢查询日志...")

	// 验证参数
	if err := ValidateAPIParams(config); err != nil {
		return "", nil, fmt.Errorf("参数验证失败: %v", err)
	}

	// 计算查询时间范围
	now := time.Now()
	// 默认获取前一天的数据
	yesterday := now.AddDate(0, 0, -1)
	startTime := FormatTime(time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location()))
	endTime := FormatTime(time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 23, 59, 59, 0, yesterday.Location()))

	logger.Infof("查询时间范围: %s 至 %s", startTime, endTime)

	// 获取所有RDS实例
	instances, err := GetAllDBInstances(config.RegionId, config.PageSize, config)
	if err != nil {
		return "", nil, fmt.Errorf("获取RDS实例列表失败: %v", err)
	}

	if len(instances) == 0 {
		logger.Info("未找到任何RDS实例")
		return "未找到任何RDS实例", nil, nil
	}

	logger.Infof("找到 %d 个RDS实例", len(instances))

	// 收集所有实例的慢查询日志
	instanceLogs := make(map[string][]SlowLogRecord)
	successCount := 0
	failCount := 0
	ignoredCount := 0

	// 创建忽略实例的映射，便于快速查找
	ignoreMap := make(map[string]bool)
	for _, id := range config.IgnoreDBInstanceID {
		ignoreMap[id] = true
	}

	for _, instance := range instances {
		// 检查是否在忽略列表中
		if ignoreMap[instance.DBInstanceId] {
			logger.Infof("实例 %s 在忽略列表中，跳过", instance.DBInstanceId)
			ignoredCount++
			continue
		}

		records, err := CollectInstanceSlowLogs(instance, startTime, endTime, config.PageSize)
		if err != nil {
			logger.Warnf("获取实例 %s 的慢查询日志失败: %v", instance.DBInstanceId, err)
			failCount++
			continue
		}
		instanceLogs[instance.DBInstanceId] = records
		successCount++
	}

	logger.Infof("成功收集 %d 个实例的慢查询日志，失败 %d 个，忽略 %d 个", successCount, failCount, ignoredCount)

	// 处理慢查询日志数据
	instanceReports := ProcessSlowLogData(instanceLogs, instances)

	// 如果没有慢查询日志，提前返回
	if len(instanceReports) == 0 {
		logger.Info("没有发现慢查询日志，不生成报告")
		return "没有发现慢查询日志", nil, nil
	}

	// 创建报告
	report := SlowLogReport{
		StartTime:       startTime,
		EndTime:         endTime,
		InstanceReports: instanceReports,
		GeneratedAt:     time.Now().Format("2006-01-02 15:04:05"),
	}

	// 生成文本报告
	textReport := GenerateTextReport(report)

	// 统计信息
	totalSlowQueries := 0
	for _, ir := range instanceReports {
		totalSlowQueries += ir.TotalCount
	}

	logger.Infof("RDS慢查询日志报告已成功生成，共包含 %d 个实例的 %d 条慢查询记录",
		len(instanceReports), totalSlowQueries)

	return textReport, &report, nil
}

// 验证API参数
func ValidateAPIParams(config RDSSlowLogConfig) error {
	// 验证区域ID
	if config.RegionId == "" {
		return fmt.Errorf("区域ID不能为空")
	}

	// 验证查询时间范围
	if config.QueryHoursAgo <= 0 {
		return fmt.Errorf("查询时间范围必须大于0小时")
	}

	// 验证慢查询阈值
	if config.SlowThreshold <= 0 {
		return fmt.Errorf("慢查询阈值必须大于0毫秒")
	}

	// 验证分页大小
	if config.PageSize <= 0 || config.PageSize > 100 {
		return fmt.Errorf("分页大小必须在1-100之间")
	}

	// 验证收件人
	if len(config.Recipients) == 0 {
		return fmt.Errorf("至少需要一个邮件接收人")
	}

	return nil
}

// 创建默认配置
func CreateDefaultRDSSlowLogConfig() RDSSlowLogConfig {
	return RDSSlowLogConfig{
		Recipients:         []string{"<EMAIL>"},
		QueryHoursAgo:      24,
		SlowThreshold:      1000,
		RegionId:           "cn-hangzhou",
		PageSize:           100,
		IgnoreDBInstanceID: []string{"rm-bp111s3k9m2o06u6j", "rm-bp1o78g1nf9kt6rhc"}, // 默认忽略测试和开发实例
		SMTPServer:         "smtp.example.com",
		SMTPPort:           25,
		SMTPUser:           "<EMAIL>",
		SMTPPassword:       "password",
		SMTPFrom:           "<EMAIL>",
	}
}

// 运行RDS慢查询日志收集器
func RunRDSSlowLogCollector() error {
	logger.Info("开始运行RDS慢查询日志收集器...")
	config := CreateDefaultRDSSlowLogConfig()
	// 收集慢查询日志并生成报告
	textReport, report, err := CollectAndGenerateRDSSlowLogs(config)
	if err != nil {
		return fmt.Errorf("收集慢查询日志失败: %v", err)
	}

	// 如果没有报告，说明没有慢查询日志
	if report == nil {
		logger.Info("没有慢查询日志，不发送邮件")
		return nil
	}
	// 输出文本报告
	logger.Info("生成的文本报告:\n" + textReport)

	// 生成HTML报告
	htmlReport := GenerateHTMLReport(*report)
	// // 获取报告日期（前一天）
	reportDate := time.Now().AddDate(0, 0, -1)
	// 发送邮件
	// subject := fmt.Sprintf("RDS慢查询日志报告 - %s", time.Now().Format("2006-01-02"))
	// err = SendEmail(config, subject, htmlReport, "html")
	// if err != nil {
	// 	return fmt.Errorf("发送邮件失败: %v", err)
	// }
	// // 保存报告到文件

	reportFileName := fmt.Sprintf("./log/rds_slow_log_report_%s.html", reportDate.Format("20060102"))
	err = os.WriteFile(reportFileName, []byte(htmlReport), 0644)
	if err != nil {
		logger.Errorf("保存报告文件失败: %v", err)
	} else {
		logger.Infof("报告已保存到文件: %s", reportFileName)
	}

	// logger.Info("RDS慢查询日志报告已成功发送")
	return nil
}

// 设置定时任务
func SetupRDSSlowLogCronJob(config RDSSlowLogConfig, cronExpression string) {
	logger.Infof("设置RDS慢查询日志收集定时任务: %s", cronExpression)
	// 这里可以使用cron库来设置定时任务
	// 例如：
	// c := cron.New()
	// c.AddFunc(cronExpression, func() {
	//     err := RunRDSSlowLogCollector(config)
	//     if err != nil {
	//         logger.Errorf("运行RDS慢查询日志收集器失败: %v", err)
	//     }
	// })
	// c.Start()
}
